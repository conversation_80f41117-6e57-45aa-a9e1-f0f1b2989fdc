<template>
  <div class="lazyComponent_wrap" :class="{ loading: !initLoad }">
    <slot v-if="initLoad"></slot>
    <slot v-else name="skeleton"></slot>
  </div>
</template>

<script lang="ts">
import { Vue, Prop, Component } from 'vue-property-decorator'
import lazyObserver from './lazy-observer'

@Component
export default class LazyComponent extends Vue {
  @Prop({ type: Function, default: (_parmas?: any, _options?: any) => Promise.resolve() }) beforeLoad!: (_parmas?: any, _options?: any) => Promise<any>
  @Prop({ type: String, default: '100px' }) rootMargin!: string

  initLoad = false

  mounted() {
    const lazyObserveInstance = lazyObserver({
      rootMargin: this.rootMargin, // 10px触发加载
      callBack: async () => {
        await this.beforeLoad(undefined, { isClientReport: true })
        this.$emit('inited')
        this.initLoad = true
      },
      observeDom: this.$el
    })

    this.$once('hook:beforeDestroy', () => {
      // 释放
      lazyObserveInstance && lazyObserveInstance!.disconnect()
    })
  }
};
</script>
